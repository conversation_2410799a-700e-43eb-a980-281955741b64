<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/white"
    android:padding="12dp">

    <!-- 顶部：分类 + 状态 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="18dp"
            android:layout_height="18dp"
            android:src="@drawable/ic_biguo_mall_study"/>

        <TextView
            android:id="@+id/tvCategory"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="学习工具"
            android:textColor="@color/tblack"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="售后中"
            android:textColor="#D53E43"
            android:textSize="14sp"/>
    </LinearLayout>

    <!-- 商品图 + 标题 + 价格（标题与价格顶对齐） -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp">

        <ImageView
            android:id="@+id/ivCover"
            android:layout_width="86dp"
            android:layout_height="86dp"
            android:scaleType="centerCrop"
            android:src="@drawable/order_book_image"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tvPrice"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="¥2500.00"
            android:textColor="@color/tblack"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/ivCover"/>

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:text="笔果AI学习机"
            android:textColor="@color/black3"
            android:textSize="16sp"
            android:maxLines="2"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            app:layout_constraintStart_toEndOf="@id/ivCover"
            app:layout_constraintTop_toTopOf="@id/ivCover"
            app:layout_constraintEnd_toStartOf="@id/tvPrice"/>

        <!-- 封面右下角的提示文案 -->
        <TextView
            android:id="@+id/tvTip"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="若个人卖家未在48小时后发货，平台自动取消"
            android:textColor="#ffff9100"
            android:textSize="10sp"
            android:drawableStart="@drawable/ic_notise_icon"
            android:drawablePadding="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toEndOf="@id/ivCover"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            app:layout_constraintVertical_bias="1.0" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="#EBEBEB"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="10dp"/>

    <!-- 实付金额 -->
    <TextView
        android:id="@+id/tvPaid"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:text="实付 ¥2500.00"
        android:textColor="@color/tblack"
        android:textSize="14sp"/>

    <!-- 操作按钮：售后进度 / 客服 / 确认收货 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="10dp">

        <TextView
            android:id="@+id/btnAfterSale"
            android:layout_width="96dp"
            android:layout_height="34dp"
            android:gravity="center"
            android:background="@drawable/bg_btn_outline_gray"
            android:text="售后进度"
            android:textColor="@color/black3"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/btnService"
            android:layout_width="96dp"
            android:layout_height="34dp"
            android:layout_marginStart="10dp"
            android:gravity="center"
            android:background="@drawable/bg_btn_outline_gray"
            android:text="联系客服"
            android:textColor="@color/black3"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/btnConfirm"
            android:layout_width="96dp"
            android:layout_height="34dp"
            android:layout_marginStart="10dp"
            android:gravity="center"
            android:background="@drawable/bg_btn_outline_red"
            android:text="确认收货"
            android:textColor="#D53E43"
            android:textSize="14sp"/>
    </LinearLayout>

    <!-- 底部间距占位 View -->
    <View
        android:layout_width="match_parent"
        android:layout_height="10dp" />

</LinearLayout>
