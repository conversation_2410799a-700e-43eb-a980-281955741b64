<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/white">

    <!-- 顶部分段头（返回 | 购物车/订单/我的闲置） -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/white"
        android:layout_marginTop="20dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:paddingTop="12dp">

        <include layout="@layout/common_segmented_header" />
    </LinearLayout>

    <!-- 三段 Tab：全部 / 待付款 / 已完成 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/topTabs"
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:background="@color/white"
        app:tabMode="fixed"
        app:tabGravity="fill"
        app:tabIndicatorColor="@color/theme"
        app:tabIndicatorHeight="2dp"
        app:tabSelectedTextColor="@color/theme"
        app:tabTextColor="@color/black3" />

    <!-- 列表区域（一个 RecyclerView 即可，后续按需切换数据） -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="#1A000000"/>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/bgc"
        tools:listitem="@layout/item_shop_order_simple" />

</LinearLayout>
