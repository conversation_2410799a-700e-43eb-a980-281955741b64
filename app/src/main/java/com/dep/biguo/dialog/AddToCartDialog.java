package com.dep.biguo.dialog;

import android.content.Context;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import android.graphics.Typeface;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.text.style.StyleSpan;

import androidx.core.content.ContextCompat;

import java.text.DecimalFormat;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.AddressBean;
import com.dep.biguo.common.CommonAdapter;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.ArrayList;
import java.util.List;

/**
 * 简版 加入购物车 弹窗：包含 颜色选择 和 配套推荐 两个列表
 */
public class AddToCartDialog extends BottomDialog {
    private static final String TAG = "AddToCart";

    private RecyclerView rvColors, rvBundles;
    private TextView btnAddCart, tvCount, btnMinus, btnPlus;
    private View addressBar;
    private TextView tvAddressTitle, tvAddressHint;

    public interface OnAddressClickListener {
        void onClick();
    }
    private OnAddressClickListener onAddressClickListener;

    public void setOnAddressClickListener(OnAddressClickListener listener){
        this.onAddressClickListener = listener;
    }
    private TextView tvPrice;

    private void setPrice(double price){
        // 格式化成 ¥1234.56，货币符号与小数部分稍小、粗体
        String full = String.format("¥%s", new DecimalFormat("0.00").format(price));
        SpannableString span = new SpannableString(full);
        int color = ContextCompat.getColor(getContext(), R.color.price_red);
        span.setSpan(new ForegroundColorSpan(color), 0, full.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        // 符号缩小到 0.8x
        span.setSpan(new RelativeSizeSpan(0.8f), 0, 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        // 整体半粗
        span.setSpan(new StyleSpan(Typeface.BOLD), 0, full.length(), Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        tvPrice.setText(span);
    }


    public void bindAddress(AddressBean address){
        if(address == null){
            tvAddressTitle.setText("请选择收货地址");
            tvAddressHint.setText("快递 包邮");
        }else{
            tvAddressTitle.setText(address.getProvinces() + address.getCity() + address.getArea());
            tvAddressHint.setText(address.getDetail());
        }
    }

    private ImageView ivThumb;

    private final List<ColorItem> colorItems = new ArrayList<>();
    private final List<String> bundleItems = new ArrayList<>();

    private ColorAdapter colorAdapter;
    private BundleAdapter bundleAdapter;

    private int count = 1;

    public interface OnConfirmListener {
        void onConfirm(int count);
    }
    private OnConfirmListener onConfirmListener;
    public void setOnConfirmListener(OnConfirmListener listener){
        this.onConfirmListener = listener;
    }

    public AddToCartDialog(@NonNull Context context) {
        super(context, R.layout.dialog_add_to_cart);
        android.util.Log.d(TAG, "AddToCartDialog created");
    }

    @Override
    public int getLayoutId() { return R.layout.dialog_add_to_cart; }

    @Override
    public void init() {
        android.util.Log.d(TAG, "AddToCartDialog init() called");
        rvColors = findViewById(R.id.rvColors);
        rvBundles = findViewById(R.id.rvBundles);
        btnAddCart = findViewById(R.id.btnAddCart);
        tvCount = findViewById(R.id.tvCount);
        btnMinus = findViewById(R.id.btnMinus);
        btnPlus = findViewById(R.id.btnPlus);
        ivThumb = findViewById(R.id.ivThumb);
        addressBar = findViewById(R.id.addressBar);
        tvAddressTitle = findViewById(R.id.tvAddressTitle);
        tvAddressHint = findViewById(R.id.tvAddressHint);
        tvPrice = findViewById(R.id.tvPrice);
        setPrice(2500.00);


        // mock thumb
        ImageLoader.loadImage(ivThumb, R.drawable.img_banner_placeholder);

        // colors: horizontal list
        rvColors.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        colorAdapter = new ColorAdapter(colorItems);
        colorAdapter.bindToRecyclerView(rvColors);

        // bundle: vertical list
        rvBundles.setLayoutManager(new LinearLayoutManager(getContext()));
        bundleAdapter = new BundleAdapter(bundleItems);
        bundleAdapter.bindToRecyclerView(rvBundles);

        btnMinus.setOnClickListener(v -> updateCount(-1));
        btnPlus.setOnClickListener(v -> updateCount(1));
        btnAddCart.setOnClickListener(v -> {
            android.util.Log.d(TAG, "===== Add to cart button clicked in dialog =====");
            android.util.Log.d(TAG, "  - Final count: " + count);
            android.util.Log.d(TAG, "  - Has listener: " + (onConfirmListener != null));
            if (onConfirmListener != null) {
                onConfirmListener.onConfirm(count);
            }
            dismiss();
        });
        addressBar.setOnClickListener(v -> {
            android.util.Log.d(TAG, "addressBar clicked");
            if(onAddressClickListener != null) onAddressClickListener.onClick();
        });

        tvCount.setText(String.valueOf(count));
    }

    private void updateCount(int delta){
        count = Math.max(1, count + delta);
        android.util.Log.d(TAG, "updateCount: newCount=" + count + ", delta=" + delta);
        tvCount.setText(String.valueOf(count));
    }

    public void setColors(List<ColorItem> data){
        colorItems.clear();
        if (data != null) colorItems.addAll(data);
        if (colorAdapter != null) colorAdapter.notifyDataSetChanged();
    }

    public void setBundles(List<String> bundles){
        bundleItems.clear();
        if (bundles != null) bundleItems.addAll(bundles);
        if (bundleAdapter != null) bundleAdapter.notifyDataSetChanged();
    }

    public static class ColorItem{
        public String name;
        public Object image; // 支持 String URL 或 资源ID
        public boolean selected;
        public ColorItem(String name, Object image){
            this.name = name; this.image = image;
        }
    }

    private static class ColorAdapter extends CommonAdapter<ColorItem> {
        ColorAdapter(List<ColorItem> data){ super(R.layout.item_color_option, data); }
        @Override
        protected void convert(BaseViewHolder holder, ColorItem item) {
            holder.setText(R.id.tvName, item.name == null ? "" : item.name);
            ImageView iv = holder.getView(R.id.ivColor);
            ImageLoader.loadImage(iv, item.image);
            View container = holder.getView(R.id.container);
            container.setBackgroundResource(item.selected ? R.drawable.bg_color_selected : R.drawable.bg_color_unselected);
            container.setOnClickListener(v -> {
                // single select
                android.util.Log.d(TAG, "Color selected: " + item.name);
                for (ColorItem ci : mData) ci.selected = false;
                item.selected = true;
                notifyDataSetChanged();
            });
        }
    }

    private static class BundleAdapter extends CommonAdapter<String> {
        private int selectedPosition = RecyclerView.NO_POSITION;
        BundleAdapter(List<String> data){ super(R.layout.item_bundle_option, data); }
        @Override
        protected void convert(BaseViewHolder holder, String item) {
            int position = holder.getAdapterPosition();
            holder.setText(R.id.tvBundleName, item);
            View root = holder.itemView;
            boolean selected = position == selectedPosition;
            // 选中样式与商品颜色一致
            root.setBackgroundResource(selected ? R.drawable.bg_color_selected : R.drawable.bg_bundle_unselected);
            root.setOnClickListener(v -> {
                android.util.Log.d(TAG, "Bundle selected: " + item + " at position " + position);
                int old = selectedPosition;
                selectedPosition = position;
                // 单选：刷新旧的和新的两项
                if (old != RecyclerView.NO_POSITION && old != selectedPosition) {
                    notifyItemChanged(old);
                }
                notifyItemChanged(selectedPosition);
            });
        }
    }
}

