package com.dep.biguo.mvp.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

public class SimpleOrderAdapter extends BaseQuickAdapter<OrderBean, BaseViewHolder> {

    public SimpleOrderAdapter(@Nullable List<OrderBean> data) {
        super(R.layout.item_shop_order_simple, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OrderBean item) {
        // 设置分类名称
        helper.setText(R.id.tvCategory, getItemCategory(item));
        
        // 设置状态文本和控制视图显示
        setupOrderStatusAndViews(helper, item);

        // 设置商品标题和价格
        if (item.getGoods_data() != null && !item.getGoods_data().isEmpty()) {
            ShopBean firstGood = item.getGoods_data().get(0);
            helper.setText(R.id.tvTitle, firstGood.getName());

            // 设置价格
            String priceText = "¥" + firstGood.getPrice();
            helper.setText(R.id.tvPrice, priceText);

            // 加载商品图片
            ImageView ivCover = helper.getView(R.id.ivCover);
            if (!TextUtils.isEmpty(firstGood.getImg())) {
                ImageLoader.loadImage(ivCover, firstGood.getImg());
            } else {
                ivCover.setImageResource(R.drawable.order_book_image);
            }
        }

        // 设置实付金额
        helper.setText(R.id.tvPaid, "实付 ¥" + item.getTotal_fee());
    }

    /**
     * 设置订单状态和控制相关视图的显示
     */
    private void setupOrderStatusAndViews(BaseViewHolder helper, OrderBean item) {
        TextView tvStatus = helper.getView(R.id.tvStatus);
        TextView tvTip = helper.getView(R.id.tvTip);
        TextView btnAfterSale = helper.getView(R.id.btnAfterSale);
        TextView btnConfirm = helper.getView(R.id.btnConfirm);

        // 判断是否为闲置商品
        if ("idle".equals(item.getType())) {
            // 闲置商品的状态处理
            setupIdleItemStatusAndViews(helper, item, tvStatus, tvTip, btnAfterSale, btnConfirm);
        } else {
            // 普通订单的状态处理
            setupOrderItemStatusAndViews(helper, item, tvStatus, tvTip, btnAfterSale, btnConfirm);
        }
    }

    /**
     * 设置闲置商品状态和控制相关视图的显示
     */
    private void setupIdleItemStatusAndViews(BaseViewHolder helper, OrderBean item,
                                           TextView tvStatus, TextView tvTip,
                                           TextView btnAfterSale, TextView btnConfirm) {
        switch (item.getState()) {
            case 1: // 在卖
                tvStatus.setText("在卖");
                tvTip.setVisibility(View.GONE);
                btnAfterSale.setVisibility(View.GONE);
                btnConfirm.setVisibility(View.VISIBLE);
                btnConfirm.setText("下架");
                break;
            case 2: // 待发货
                tvStatus.setText("待发货");
                tvTip.setVisibility(View.VISIBLE);
                tvTip.setText("买家已付款，请尽快发货");
                btnAfterSale.setVisibility(View.GONE);
                btnConfirm.setVisibility(View.VISIBLE);
                btnConfirm.setText("发货");
                break;
            case 3: // 已完成
                tvStatus.setText("已完成");
                tvTip.setVisibility(View.GONE);
                btnAfterSale.setVisibility(View.GONE);
                btnConfirm.setVisibility(View.VISIBLE);
                btnConfirm.setText("再次发布");
                break;
            default:
                tvStatus.setText("在卖");
                tvTip.setVisibility(View.GONE);
                btnAfterSale.setVisibility(View.GONE);
                btnConfirm.setVisibility(View.VISIBLE);
                btnConfirm.setText("下架");
                break;
        }
    }

    /**
     * 设置普通订单状态和控制相关视图的显示
     */
    private void setupOrderItemStatusAndViews(BaseViewHolder helper, OrderBean item,
                                            TextView tvStatus, TextView tvTip,
                                            TextView btnAfterSale, TextView btnConfirm) {
        // tvStatus只显示三种状态：售后中、待发货、订单已完成
        if (item.getState() == 5) {
            // 售后中：显示"售后中"，隐藏tvTip，显示btnConfirm
            tvStatus.setText("售后中");
            tvTip.setVisibility(View.GONE);
            btnAfterSale.setVisibility(View.VISIBLE);
            btnConfirm.setVisibility(View.VISIBLE);
            btnConfirm.setText("确认收货"); // 售后中保持原文案
        } else if (item.getState() == 2) {
            // 待发货：显示"待发货"，显示tvTip，隐藏btnAfterSale，隐藏btnConfirm
            tvStatus.setText("待发货");
            tvTip.setVisibility(View.VISIBLE);
            btnAfterSale.setVisibility(View.GONE);
            btnConfirm.setVisibility(View.GONE);
        } else {
            // 其他所有状态都显示"订单已完成"：隐藏tvTip，隐藏btnAfterSale，显示btnConfirm
            tvStatus.setText("订单已完成");
            tvTip.setVisibility(View.GONE);
            btnAfterSale.setVisibility(View.GONE);
            btnConfirm.setVisibility(View.VISIBLE);
            btnConfirm.setText("我要评价"); // 订单已完成时改为"我要评价"
        }
    }

    /**
     * 获取订单分类名称
     */
    private String getItemCategory(OrderBean item) {
        if (TextUtils.isEmpty(item.getType())) {
            return "学习工具";
        }

        switch (item.getType()) {
            case "book":
                return "图书教材";
            case "video":
                return "视频课程";
            case "vip":
                return "VIP题库";
            case "idle":
                return "闲置商品";
            default:
                return "学习工具";
        }
    }
    
    /**
     * 获取订单状态文字
     */
    private String getOrderStatusText(OrderBean item) {
        if (item.getRefund_status() > 0) {
            switch (item.getRefund_status()) {
                case 1:
                    return "退款申请中";
                case 2:
                    return "退款已驳回";
                case 3:
                    return "已退款";
                default:
                    return "售后中";
            }
        }

        switch (item.getState()) {
            case 1:
                return "待付款";
            case 2:
                return "待发货";
            case 3:
                return "待收货";
            case 4:
                return "待评价";
            case 5:
                return "售后中";
            case -1:
                return "已关闭";
            case -2:
                return "已取消";
            default:
                return "未知状态";
        }
    }
}