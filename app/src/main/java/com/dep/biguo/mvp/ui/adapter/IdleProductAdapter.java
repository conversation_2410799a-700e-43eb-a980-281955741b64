package com.dep.biguo.mvp.ui.adapter;

import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.dep.biguo.R;
import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.utils.image.ImageLoader;

import java.util.List;

/**
 * 闲置商品适配器
 */
public class IdleProductAdapter extends BaseQuickAdapter<OrderBean, BaseViewHolder> {

    public IdleProductAdapter(@Nullable List<OrderBean> data) {
        super(R.layout.item_idle_product, data);
    }

    @Override
    protected void convert(BaseViewHolder helper, OrderBean item) {
        // 设置商品标题
        if (item.getGoods_data() != null && !item.getGoods_data().isEmpty()) {
            ShopBean firstGood = item.getGoods_data().get(0);
            helper.setText(R.id.tvProductTitle, firstGood.getName());

            // 设置价格 - 使用分离的TextView
            TextView tvPriceCurrency = helper.getView(R.id.tvPriceCurrency);
            TextView tvPriceAmount = helper.getView(R.id.tvPriceAmount);
            tvPriceCurrency.setText("¥");
            tvPriceAmount.setText(firstGood.getPrice());

            // 加载商品图片
            ImageView ivProductImage = helper.getView(R.id.ivProductImage);
            if (!TextUtils.isEmpty(firstGood.getImg())) {
                ImageLoader.loadImage(ivProductImage, firstGood.getImg());
            } else {
                ivProductImage.setImageResource(R.drawable.icon_book);
            }
        }

        // 设置状态和操作按钮
        setupStatusAndAction(helper, item);
    }

    /**
     * 设置状态标签和操作按钮
     */
    private void setupStatusAndAction(BaseViewHolder helper, OrderBean item) {
        // 使用状态管理工具类设置UI
        com.dep.biguo.utils.IdleProductStateManager.setupUIByState(helper, item);

        // 设置按钮点击事件
        TextView btnAction = helper.getView(R.id.btnAction);
        TextView btnAction2 = helper.getView(R.id.btnAction2);

        btnAction.setOnClickListener(v -> {
            handleActionClick(item, helper.getAdapterPosition(), "action");
        });

        btnAction2.setOnClickListener(v -> {
            handleActionClick(item, helper.getAdapterPosition(), "action2");
        });
    }

    /**
     * 处理操作按钮点击事件
     */
    private void handleActionClick(OrderBean item, int position, String actionType) {
        switch (item.getState()) {
            case 1: // 状态1：隐藏btnAction2，将btnAction文案改成"下架"
                if ("action".equals(actionType)) {
                    // btnAction - 下架
                    handleOffShelfAction(item, position);
                }
                break;

            case 2: // 状态2：隐藏tvStatusTag
                if ("action".equals(actionType)) {
                    // btnAction - 编辑
                    handleEditAction(item, position);
                } else if ("action2".equals(actionType)) {
                    // btnAction2 - 下架
                    handleOffShelfAction(item, position);
                }
                break;

            case 3: // 状态3：隐藏btnAction2和tvStatusTag
                if ("action".equals(actionType)) {
                    // btnAction - 再次发布
                    handleRepublishAction(item, position);
                }
                break;

            default:
                if ("action".equals(actionType)) {
                    // btnAction - 编辑
                    handleEditAction(item, position);
                } else if ("action2".equals(actionType)) {
                    // btnAction2 - 下架
                    handleOffShelfAction(item, position);
                }
                break;
        }
    }

    /**
     * 处理下架操作
     */
    private void handleOffShelfAction(OrderBean item, int position) {
        // TODO: 实现下架逻辑
        // 可以在这里添加确认对话框，然后调用API下架商品
    }

    /**
     * 处理编辑操作
     */
    private void handleEditAction(OrderBean item, int position) {
        // TODO: 实现编辑逻辑
        // 可以跳转到编辑页面
    }

    /**
     * 处理再次发布操作
     */
    private void handleRepublishAction(OrderBean item, int position) {
        // TODO: 实现再次发布逻辑
        // 可以跳转到发布页面或直接重新发布
    }
}
