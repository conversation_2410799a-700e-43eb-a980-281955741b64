package com.dep.biguo.mvp.ui.activity;

import android.os.Bundle;
import android.view.View;
import android.graphics.Rect;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.dep.biguo.R;
import com.dep.biguo.mvp.ui.adapter.SimpleOrderAdapter;
import com.google.android.material.tabs.TabLayout;
import com.jess.arms.base.BaseActivity;
import com.jess.arms.di.component.AppComponent;

import com.dep.biguo.bean.OrderBean;
import com.dep.biguo.bean.ShopBean;

import java.util.ArrayList;
import java.util.List;

/**
 * 简化版"订单"页面：不复用项目现有的复杂页面结构，仅用于从购物车头部跳转展示
 * 三段 Tab（全部/待付款/已完成）与一个列表。
 */
public class ShopOrderSimpleActivity extends BaseActivity {

    private TabLayout topTabs;
    private RecyclerView recyclerView;
    private SimpleOrderAdapter adapter;

    // 模拟数据
    private List<OrderBean> allOrders = new ArrayList<>();
    private List<OrderBean> pendingPaymentOrders = new ArrayList<>();
    private List<OrderBean> completedOrders = new ArrayList<>();

    public static void start(android.content.Context context){
        context.startActivity(new android.content.Intent(context, ShopOrderSimpleActivity.class));
    }

    @Override
    public void setupActivityComponent(AppComponent appComponent) { }

    @Override
    public int initView(@Nullable Bundle savedInstanceState) {
        setContentView(R.layout.shop_order_simple_activity);
        topTabs = findViewById(R.id.topTabs);
        recyclerView = findViewById(R.id.recyclerView);
        View backHeader = findViewById(R.id.ivBackHeader);
        if (backHeader != null) backHeader.setOnClickListener(v -> finish());

        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        // 添加 ItemDecoration 来实现底部间距
        recyclerView.addItemDecoration(new ItemSpacingDecoration(10));
        // 使用新的适配器
        adapter = new SimpleOrderAdapter(new java.util.ArrayList<>());
        recyclerView.setAdapter(adapter);

        topTabs.addTab(topTabs.newTab().setText("全部"));
        topTabs.addTab(topTabs.newTab().setText("待付款"));
        topTabs.addTab(topTabs.newTab().setText("已完成"));

        topTabs.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override public void onTabSelected(TabLayout.Tab tab) { loadData(tab.getPosition()); }
            @Override public void onTabUnselected(TabLayout.Tab tab) { }
            @Override public void onTabReselected(TabLayout.Tab tab) { }
        });

        // 初始化模拟数据
        initMockData();
        
        loadData(0);
        return 0;
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) { }

    private void initMockData() {
        // 创建"全部"订单数据
        for (int i = 0; i < 5; i++) {
            OrderBean order = new OrderBean();
            order.setOrder_number("ORD20230810000" + (i + 1));
            order.setCounts(2);
            order.setTotal_fee(String.valueOf(100 + i * 50));
            order.setName("订单名称 " + (i + 1));
            order.setState(i % 3 == 0 ? 1 : 
                          i % 3 == 1 ? 2 : 
                          4);
            order.setType("book");
            
            // 添加商品数据
            List<ShopBean> goodsList = new ArrayList<>();
            for (int j = 0; j < 2; j++) {
                ShopBean shop = new ShopBean();
                shop.setId(j + 1);
                shop.setName("商品 " + (j + 1) + " - 订单 " + (i + 1));
                shop.setPrice(String.valueOf(50 + j * 20));
                shop.setPreferential_price(String.valueOf(40 + j * 15));
                shop.setImg("http://example.com/image" + (j + 1) + ".jpg");
                shop.setCount(1);
                goodsList.add(shop);
            }
            order.setGoods_data(goodsList);
            
            allOrders.add(order);
            
            // 根据订单状态分类到不同的列表
            if (order.getState() == 1) {
                pendingPaymentOrders.add(order);
            } else if (order.getState() == 4) {
                completedOrders.add(order);
            }
        }
    }

    private void loadData(int tabIndex){
        // 根据选中的 Tab 加载相应的数据
        switch (tabIndex) {
            case 0: // 全部
                adapter.setNewData(new ArrayList<>(allOrders));
                break;
            case 1: // 待付款
                adapter.setNewData(new ArrayList<>(pendingPaymentOrders));
                break;
            case 2: // 已完成
                adapter.setNewData(new ArrayList<>(completedOrders));
                break;
            default:
                adapter.setNewData(new ArrayList<>());
                break;
        }
    }

    /**
     * 自定义 ItemDecoration 来添加 item 间距
     */
    private static class ItemSpacingDecoration extends RecyclerView.ItemDecoration {
        private final int spacing;

        public ItemSpacingDecoration(int spacing) {
            this.spacing = spacing;
        }

        @Override
        public void getItemOffsets(@NonNull Rect outRect, @NonNull View view,
                                 @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
            // 转换 dp 到 px
            int spacingPx = (int) (spacing * parent.getContext().getResources().getDisplayMetrics().density);

            // 为每个 item 添加底部间距
            outRect.bottom = spacingPx;

            // 如果是第一个 item，也可以添加顶部间距
            if (parent.getChildAdapterPosition(view) == 0) {
                outRect.top = spacingPx;
            }
        }
    }
}
