package com.dep.biguo.utils;

/**
 * 测试配置类
 * 用于控制应用的测试模式
 */
public class TestConfig {
    
    /**
     * 是否使用本地测试数据
     * 设置为 true 时，加入购物车等功能将使用模拟数据而不是真实的网络请求
     * 设置为 false 时，使用真实的服务器接口
     */
    public static final boolean USE_LOCAL_TEST_DATA = true;
    
    /**
     * 测试商品ID
     * 当使用本地测试数据时，这些ID会返回成功响应
     */
    public static final int[] TEST_GOODS_IDS = {10, 11, 12, 13, 14, 15, 200, 201, 301};
    
    /**
     * 检查商品ID是否为测试ID
     */
    public static boolean isTestGoodsId(int goodsId) {
        for (int id : TEST_GOODS_IDS) {
            if (id == goodsId) {
                return true;
            }
        }
        return false;
    }
}
