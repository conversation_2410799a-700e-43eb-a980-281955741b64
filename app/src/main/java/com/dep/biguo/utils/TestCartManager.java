package com.dep.biguo.utils;

import com.dep.biguo.bean.ShopBean;
import java.util.HashSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 测试购物车管理器
 * 用于本地测试模式下管理购物车数据
 */
public class TestCartManager {
    
    private static TestCartManager instance;
    private Map<Integer, ShopBean> cartItems = new HashMap<>();
    // 本地测试：按商品ID临时覆写分类
    private final Map<Integer, String> categoryOverrides = new HashMap<>();
    
    private TestCartManager() {
        // 初始化时添加一些测试商品到购物车
        initTestCart();
    }
    
    public static TestCartManager getInstance() {
        if (instance == null) {
            synchronized (TestCartManager.class) {
                if (instance == null) {
                    instance = new TestCartManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 添加商品到购物车
     */
    public void addToCart(int goodsId) {
        android.util.Log.d("AddToCart", "TestCartManager.addToCart - Adding goods_id: " + goodsId);
        
        ShopBean item = cartItems.get(goodsId);
        if (item != null) {
            // 商品已存在，增加数量
            item.setCount(item.getCount() + 1);
            android.util.Log.d("AddToCart", "Item already in cart, new count: " + item.getCount());
        } else {
            // 创建新商品
            item = createTestProduct(goodsId);
            // 应用一次性分类覆写（若有）
            String override = categoryOverrides.remove(goodsId);
            if (override != null && override.length() > 0) {
                item.setCategory(override);
                applyGroupIdByCategory(item);
            }
            cartItems.put(goodsId, item);
            android.util.Log.d("AddToCart", "New item added to cart");
        }
    }
    
    /**
     * 获取购物车中的所有商品
     */
    public List<ShopBean> getCartItems() {
        android.util.Log.d("AddToCart", "TestCartManager.getCartItems - Total items: " + cartItems.size());
        return new ArrayList<>(cartItems.values());
    }
    
    /**
     * 删除购物车商品
     */
    public void removeFromCart(int goodsId) {
        cartItems.remove(goodsId);
        android.util.Log.d("AddToCart", "TestCartManager.removeFromCart - Removed goods_id: " + goodsId);
    }
    
    /**
     * 清空购物车
     */
    public void clearCart() {
        cartItems.clear();
        android.util.Log.d("AddToCart", "TestCartManager.clearCart - Cart cleared");
    }
    
    /**
     * 更新商品数量
     */
    public void updateItemCount(int goodsId, int count) {
        ShopBean item = cartItems.get(goodsId);
        if (item != null) {
            item.setCount(count);
            android.util.Log.d("AddToCart", "TestCartManager.updateItemCount - goods_id: " + goodsId + ", new count: " + count);
        }
    }
    
    /**
     * 创建测试商品
     */
    private ShopBean createTestProduct(int goodsId) {
        ShopBean item = new ShopBean();
        item.setId(goodsId);
        
        switch (goodsId) {
            case 2:
                item.setName("笔果题库VIP会员 - 学习工具");
                item.setImg("https://example.com/product2_vip.jpg");
                item.setPrice("99.00");
                item.setPreferential_price("79.00");
                item.setDescribe("海量题库，智能练习");
                item.setCategory("学习工具");
                item.setPostage("0");
                break;
            case 10:
                item.setName("笔果AI学习机 - 智能学习助手");
                item.setImg("https://example.com/product1.jpg");
                item.setPrice("2999.00");
                item.setPreferential_price("2500.00");
                item.setDescribe("智能AI辅导，个性化学习方案");
                item.setCategory("学习工具");
                item.setPostage("0");
                break;
            case 11:
                item.setName("笔果考试宝典 - 自考本科");
                item.setImg("https://example.com/product2.jpg");
                item.setPrice("199.00");
                item.setPreferential_price("168.00");
                item.setDescribe("全面复习资料，轻松通过考试");
                item.setCategory("学习工具");
                item.setPostage("0");
                break;
            case 12:
                item.setName("马克思主义基本原理 - 二手");
                item.setImg("https://example.com/product3.jpg");
                item.setPrice("30.00");
                item.setPreferential_price("15.00");
                item.setDescribe("9成新，无笔记");
                item.setCategory("二手教材");
                item.setPostage("8");
                break;
            case 13:
                item.setName("高等数学 - 二手教材");
                item.setImg("https://example.com/product4.jpg");
                item.setPrice("40.00");
                item.setPreferential_price("20.00");
                item.setDescribe("8成新，有少量笔记");
                item.setCategory("二手教材");
                item.setPostage("8");
                break;
            case 14:
                item.setName("新疆红枣 - 助农");
                item.setImg("https://example.com/product5.jpg");
                item.setPrice("59.00");
                item.setPreferential_price("39.00");
                item.setDescribe("新疆和田大枣，自然晾晒");
                item.setCategory("助农项目");
                item.setPostage("12");
                break;
            case 15:
                item.setName("云南普洱茶 - 助农");
                item.setImg("https://example.com/product6.jpg");
                item.setPrice("168.00");
                item.setPreferential_price("128.00");
                item.setDescribe("云南古树普洱，原产地直供");
                item.setCategory("助农项目");
                item.setPostage("10");
                break;
            case 200:
                item.setName("大学英语四级词汇 - 二手");
                item.setImg("https://example.com/product200.jpg");
                item.setPrice("25.00");
                item.setPreferential_price("12.00");
                item.setDescribe("8成新，有一些笔记");
                item.setCategory("二手教材");
                item.setPostage("8");
                break;
            case 201:
                item.setName("计算机操作系统 - 二手");
                item.setImg("https://example.com/product201.jpg");
                item.setPrice("35.00");
                item.setPreferential_price("18.00");
                item.setDescribe("9成新，无笔记");
                item.setCategory("二手教材");
                item.setPostage("8");
                break;
            case 301:
                item.setName("山东大果 - 助农");
                item.setImg("https://example.com/product301.jpg");
                item.setPrice("45.00");
                item.setPreferential_price("29.00");
                item.setDescribe("山东烟台红富士苹果");
                item.setCategory("助农项目");
                item.setPostage("10");
                break;
            case 302:
                // 无核沃柑（助农），用于失效商品展示
                item.setName("无核沃柑");
                item.setImg("https://example.com/product302.jpg");
                item.setPrice("30.00");
                item.setPreferential_price("0");
                item.setDescribe("10斤");
                item.setCategory("助农项目");
                item.setTag("助农");
                item.setPostage("0");
                break;
            case 304:
                item.setName("考研英语真题集 - 学习工具");
                item.setImg("https://example.com/product304.jpg");
                item.setPrice("88.00");
                item.setPreferential_price("68.00");
                item.setDescribe("历年考研英语真题详解");
                item.setCategory("学习工具");
                item.setPostage("0");
                break;
            default:
                item.setName("测试商品 " + goodsId);
                item.setImg("https://example.com/default.jpg");
                item.setPrice("99.00");
                item.setPreferential_price("88.00");
                item.setDescribe("这是一个测试商品");
                item.setCategory("学习工具"); // 默认分类
                item.setPostage("0");
                break;
            case 20:
                item.setName("失效商品1 - 学习工具");
                item.setImg("https://example.com/invalid1.jpg");
                item.setPrice("99.00");
                item.setPreferential_price("79.00");
                item.setDescribe("这是一个失效的学习工具商品");
                item.setCategory("学习工具");
                item.setPostage("0");
                break;
            case 21:
                item.setName("失效商品2 - 二手教材");
                item.setImg("https://example.com/invalid2.jpg");
                item.setPrice("50.00");
                item.setPreferential_price("30.00");
                item.setDescribe("这是一个失效的二手教材商品");
                item.setCategory("二手教材");
                item.setPostage("8");
                break;
        }
        
        // 根据分类设置group_id
        applyGroupIdByCategory(item);
        
        item.setCount(1);
        item.setQuantity(99); // 设置默认库存为99
        return item;
    }

    private void applyGroupIdByCategory(ShopBean item){
        String c = item.getCategory();
        if ("二手教材".equals(c)) item.setGroup_id(2);
        else if ("助农项目".equals(c)) item.setGroup_id(3);
        else item.setGroup_id(1);
    }

    /**
     * 本地测试：在下次将某个 goodsId 加入购物车时，强制覆盖其分类
     */
    public void overrideCategoryForGoods(int goodsId, String category){
        if (category == null) return;
        categoryOverrides.put(goodsId, category);
    }
    
    /**
     * 初始化测试购物车数据
     */
    private void initTestCart() {
        // 清空现有数据
        cartItems.clear();
        
        // 添加一些默认的测试商品
        // 学习工具类
        ShopBean item1 = createTestProduct(10);
        cartItems.put(10, item1);
        
        // 二手教材类  
        ShopBean item2 = createTestProduct(12);
        cartItems.put(12, item2);
        
        // 助农项目类
        ShopBean item3 = createTestProduct(14);
        cartItems.put(14, item3);
        
        // 添加一些失效商品的测试数据
        ShopBean invalidItem1 = createTestProduct(20);
        invalidItem1.setIs_invalid(true);
        cartItems.put(20, invalidItem1);
        
        ShopBean invalidItem2 = createTestProduct(21);
        invalidItem2.setIs_invalid(true);
        cartItems.put(21, invalidItem2);
        
        // 新增：无核沃柑（助农）失效示例
        ShopBean invalidItem3 = createTestProduct(302);
        invalidItem3.setIs_invalid(true);
        cartItems.put(302, invalidItem3);
        
        android.util.Log.d("AddToCart", "TestCartManager initialized with " + cartItems.size() + " test items");
    }
    
    // 删除失效商品
    public void deleteInvalidItems(List<ShopBean> invalidItems) {
        for (ShopBean item : invalidItems) {
            cartItems.remove(item.getId());
        }
    }
}
