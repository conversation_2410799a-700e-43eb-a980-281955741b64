package com.dep.biguo.utils;

import android.content.Context;
import android.widget.ImageView;

import com.biguo.utils.util.DisplayHelper;
import com.youth.banner.loader.ImageLoader;

/**
 * @Author: created by biguo
 * @CreatedDate :2019/8/27
 * @Description:
 */
public class BannerRoundImageLoader extends ImageLoader {
    @Override
    public void displayImage(Context context, Object path, ImageView imageView) {
        // 支持 res:// 资源或网络
        if (path instanceof String && ((String) path).startsWith("res://")) {
            try {
                String src = (String) path;
                String[] parts = src.split("/"); // res://drawable/name
                String resType = parts[2];
                String resName = parts[3];
                int resId = imageView.getResources().getIdentifier(resName, resType, imageView.getContext().getPackageName());
                com.dep.biguo.utils.image.ImageLoader.loadRadiusImage(imageView, resId, DisplayHelper.dp2px(context, 5));
                return;
            } catch (Exception ignore) {}
        }
        com.dep.biguo.utils.image.ImageLoader.loadRadiusImage(imageView, path, DisplayHelper.dp2px(context, 5));
    }
}
