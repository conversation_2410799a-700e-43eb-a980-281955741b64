<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.dep.biguo">

    <!--角标权限-->
    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE"/>
    <!--安装权限-->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
    <!-- 高德地图 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_CONFIGURATION"/>
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <!-- CC直播添加 -->
    <uses-permission android:name="android.permission.VIBRATE" />
    <!--<uses-permission android:name="android.permission.RECORD_AUDIO" />-->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />
    <uses-feature
        android:glEsVersion="0x00020000"
        android:required="true" />

    <!--微信登录 适配Android 11 start-->
    <queries>
        <package android:name="com.tencent.mm" />
        <package android:name="com.tencent.mobileqq" />
        <package android:name="com.qzone" />
    </queries>
    <!--微信登录 end-->

    <!--跳转OPPO应用商店评论，需要加入这个标签获取软件商店信息，传送门：https://open.oppomobile.com/new/developmentDoc/info?id=11038-->
    <queries>
        <package android:name="com.heytap.market"/>
    </queries>


    <!--穿山甲所需权限-->
    <permission
        android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN"
        android:protectionLevel="signature" />
    <uses-permission android:name="${applicationId}.openadsdk.permission.TT_PANGOLIN" />
    <!--穿山甲包含读取应用列表的权限，移除这个权限-->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:node="remove"/>


    <application
        android:name="com.jess.arms.base.BaseApplication"
        android:allowBackup="false"
        android:icon="${app_icon}"
        android:label="${app_name}"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:hardwareAccelerated="true"
        tools:replace="android:allowBackup,android:name,android:icon"
        android:usesCleartextTraffic="true"
        android:requestLegacyExternalStorage="true"
        android:allowNativeHeapPointerTagging="false"><!--保利威直播提示升级targetSdkVersion到30及以上需要关闭指针标记-->
        <!--configChanges参数说明
             orientation、screenSize：旋转屏幕不重新经历activity的生命周期
             smallestScreenSize、screenLayout、orientation、screenSize：分屏不重新经历activity的生命周期
        -->

        <activity android:name=".mvp.ui.activity.SplashTowActivity"
            android:theme="@style/AppTheme.SplashStyle"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <!-- 添加一个用于处理长按事件 -->
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />
        </activity>

        <activity
            android:name=".mvp.ui.activity.LocationRequestActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.MainActivity"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.CKMainActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.JSZMainActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.KJMainActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.JZSMainActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.RLZYMainActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.YYDJMainActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SkillActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SkillHomeActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SkillSelectedActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SkillSelectedLayerActivity"
            android:screenOrientation="portrait"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SkillAllTypeVideoActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.WorkPromoteActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.GroupCommentActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SchoolProfessionActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.BecomeHeadActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.MyCouponActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity android:name=".mvp.ui.activity.SchoolRecommendActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <activity
            android:name=".mvp.ui.activity.VideoDetailActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden" />

        <activity android:name=".mvp.ui.activity.MyVideoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SettingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.UserinfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ZkUserInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.UserInfoEditActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.RechargeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.RechargeV2Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.LoginTypeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ZkLoginActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ZkOperatePhoneActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ZkUpdatePasswordActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.RechargeDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.AboutActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.FunctionIntroductionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.AppInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="sensor"/>

        <activity android:name=".mvp.ui.activity.HtmlActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.UpdatePasswordActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.PayTuitionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.PayTuitionHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TuitionDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.UploadEnrollInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.WechatActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.FeedbackActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ArticleActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CircleDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CircleMoreCommentActivity"
            android:theme="@style/Transparent"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CirclePushActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CircleMessageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ProvinceActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SelectSkillActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CourseActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TestPlanTableActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.StudyActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InternetStudyGoodsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InputInternetStudyInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TopicActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TruePaperActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TruePaperDownloadFileActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.DownloadManageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ChapterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.RankActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.BiguoVipActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.BiguoVipOpenActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ImageActivity"
            android:theme="@style/FullWindow"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="sensor"/>

        <activity android:name=".mvp.ui.activity.MyActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.VipActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SecretActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SecretListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TestPaperListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.PracticeV3Activity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"/>

        <activity android:name=".mvp.ui.activity.AiAnalysisActivity"
            android:theme="@style/Transparent"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"/>

        <activity android:name=".mvp.ui.activity.PracticeResultV2Activity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"/>

        <activity android:name=".mvp.ui.activity.RealInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.H5PayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.DayCardV3Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.IntegralExchangeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SurveyActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CourseFeedbackActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SimuPaperActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SearchUrlActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.IntegralConvertActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TransferActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ShopDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ProductDetailStandaloneActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <!-- 二手商品详情 -->
        <activity android:name=".mvp.ui.activity.SecondHandDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.AddressMsgActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ShopCartActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InvalidItemsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.IndentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.OrderListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ReceiptApplyActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ReceiptAddTitleActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.IndentTranActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.IndentDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.QuestionnaireActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.OrderPayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.OrderDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.VideoTypeListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CommentReportActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.MessageCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.MessageListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.FeedbackListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.FeedbackTalkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.NewUserActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.GroupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SkillGroupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.GroupLaunchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.GroupDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait" />

        <activity android:name=".mvp.ui.activity.AllVideoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.AllVipActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.VideoListActivity"
            android:theme="@style/AppTheme.Html"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.HtmlVideoActivity"
            android:theme="@style/AppTheme.Html"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.HtmlVideoUrlActivity"
            android:theme="@style/AppTheme.Html"
            android:launchMode="singleTask"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TruePaperAllActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TruePaperAllNewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TruePaperNewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.SvipActivity"
            android:screenOrientation="portrait" />

        <activity android:name=".mvp.ui.activity.CKStudydataUploadActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CKScoreActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.CKStudydataActivity"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.JSZProvinceActivity"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.VipCourseDetailActivity"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.VipCourseClassActivity"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.VipCourseClassDetailActivity"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.VipAllCourseActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InformationEnterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ProfessionSchoolActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CkProfessionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.WechatBindActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ProvinceV2Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CityActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ZkSettingActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TextBooksActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.AddressListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CircleTopicActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.CircleTopicListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.VideoActivityActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.TopicListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InviteMainActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InviteTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InviteTaskRewardActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.InviteCashActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ExchangDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.IncomeDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".mvp.ui.activity.OrderSecretActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>
        <activity
            android:name=".mvp.ui.activity.InviteFriendActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ModifyAlipayAccountActivity"
            android:theme="@style/Transparent"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.SchoolDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.GroupGoodsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.LiveClandestineActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.LivePlanListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.ScholarshipRankActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.SkillGroupGoodsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.MakeStudyPlanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.StudyPlanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.StudyFileDownloadFileActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.PaySuccessActivity"
            android:launchMode="singleTask"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationStudyGoodsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationStudyReportActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationStudySearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationQualificationsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationReservationHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationCourseDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationReservationRoomActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationDeductionCardActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.PlayVideoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationBannerResActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.OrganizationCityActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.CounsellingDazikaoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.CounsellingZixuanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.CounsellingDazikaoReportActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.CounsellingZixuanReportActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.ClassTeacherEvaluateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.RewardCollectActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.RewardCollectHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.InviteToIntroduceActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity
            android:name=".mvp.ui.activity.InviteToIntroduceHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.OrganizationRecommendActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.DeviceCheckActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.FreeTopicActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.ErrorCollActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.SelectCourseActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.GraduateProxyActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.GraduateProxyTableActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.GraduateProxyProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
            android:screenOrientation="portrait"/>

        <activity android:name=".mvp.ui.activity.PdfActivity"/>

        <!-- Scheme跳转 start-->
            <activity
                android:name=".mvp.ui.activity.SchemeActivity"
                android:screenOrientation="portrait"
                android:configChanges="orientation|screenSize|keyboard|keyboardHidden"
                android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen">
                <intent-filter>
                    <action android:name="android.intent.action.VIEW" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.BROWSABLE" />
                    <data android:scheme="biguo" android:host="www.biguotk.com" />
                </intent-filter>
            </activity>
        <!-- Scheme跳转 end-->

        <!-- arms配置 start-->
            <meta-data
                android:name="com.dep.biguo.app.GlobalConfiguration"
                android:value="ConfigModule" />
        <!-- arms配置 end-->

        <!-- 高德地图 1e363b28e67ef0e8ba21aa8ef7c97cd1 start-->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="f778e76adbea9f320130bd44fbc868c9" />

            <service android:name="com.amap.api.location.APSService" />
        <!-- 高德地图 end-->

        <!--自定义的一个service，用来检测一键登录的SDK是否可以调起-->
        <service android:name=".widget.OneKeyService" />

        <!--极光一键登录 start-->
            <activity
                android:name="cn.jiguang.verifysdk.CtLoginActivity"
                android:configChanges="orientation|keyboardHidden|screenSize"
                android:launchMode="singleTop"
                android:screenOrientation="unspecified" />
        <!--极光一键登录 end-->

        <!-- QQ分享 start-->
            <activity
                android:name="com.tencent.tauth.AuthActivity"
                android:launchMode="singleTask"
                android:noHistory="true">
                <intent-filter>
                    <action android:name="android.intent.action.VIEW" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.BROWSABLE" />
                    <data android:scheme="${qqappid}" />
                </intent-filter>
            </activity>

            <activity
                android:name="com.tencent.connect.common.AssistActivity"
                android:configChanges="orientation|keyboardHidden|screenSize"
                android:theme="@android:style/Theme.Translucent.NoTitleBar"
                tools:replace="android:configChanges"/>
        <!-- QQ分享 end-->

        <!--微信分享/支付 start-->
            <activity
                android:name=".wxapi.WXEntryActivity"
                android:configChanges="keyboardHidden|orientation|screenSize"
                android:exported="true"
                android:taskAffinity="${applicationId}"
                android:launchMode="singleTask"
                android:theme="@android:style/Theme.Translucent.NoTitleBar" />

            <activity
                android:name=".wxapi.WXPayEntryActivity"
                android:configChanges="keyboardHidden|orientation|screenSize"
                android:exported="true"
                android:theme="@android:style/Theme.Translucent.NoTitleBar" />

            <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.fileprovider"
                android:exported="false"
                tools:replace="android:authorities"
                android:grantUriPermissions="true">
                <meta-data
                    tools:replace="android:resource"
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/util_file_paths"/>
            </provider>
        <!--微信分享/支付 end-->

        <!-- 友盟推送：以下为基本配置信息，需要自行添加至您的AndroidManifest文件中 start-->
            <activity
                android:name=".utils.umengPush.MfrMessageActivity"
                android:exported="true"
                android:launchMode="singleTask">
                <intent-filter>
                    <action android:name="android.intent.action.VIEW" />
                    <category android:name="android.intent.category.DEFAULT" />
                    <category android:name="android.intent.category.BROWSABLE" />
                    <data
                        android:host="${applicationId}"
                        android:path="/thirdpush"
                        android:scheme="agoo" />
                </intent-filter>
            </activity>

            <!--友盟配置信息 start-->
                <meta-data
                    android:name="UMENG_APPKEY"
                    android:value="599a6c1f4ad15603ce0011b4" />
                <meta-data
                    android:name="UMENG_MESSAGE_SECRET"
                    android:value="e96fc2695dd56d9d6be1d8fa44569669"/>
            <!--友盟配置信息 end-->

            <!--友盟多渠道打包 value的值有：biguo,vivo,huawei,yingyongbao,oppo,xiaomi    start-->
                <meta-data
                    android:name="UMENG_CHANNEL"
                    android:value="biguo" />
            <!--友盟多渠道打包 end-->

            <!--华为厂商通道 start-->
                <meta-data
                    android:name="com.huawei.hms.client.appid"
                    android:value="appid=100054937" />
            <!--华为厂商通道 end-->

            <!--VIVO厂商通道 start-->
                <meta-data android:name="com.vivo.push.api_key" android:value="c4b22d15c424ce2a3b9ce2dddd276019"/>
                <meta-data android:name="com.vivo.push.app_id" android:value="100146630"/>
            <!--VIVO厂商通道 end-->

            <!-- 小米推送兼容 兼容环信IM-->
                <receiver
                    android:name="org.android.agoo.xiaomi.MiPushBroadcastReceiver"
                    tools:node="remove"/>
                <receiver
                    android:name=".utils.umengPush.MyXMPushMessageReceiver"
                    android:exported="true" >
                    <intent-filter>
                        <action android:name="com.xiaomi.mipush.RECEIVE_MESSAGE" />
                    </intent-filter>
                    <intent-filter>
                        <action android:name="com.xiaomi.mipush.MESSAGE_ARRIVED" />
                    </intent-filter>
                    <intent-filter>
                        <action android:name="com.xiaomi.mipush.ERROR" />
                    </intent-filter>
                </receiver>
            <!-- 小米推送兼容 -->
        <!-- 友盟推送 end-->

        <!--极光推送 start-->
            <!-- Since JCore2.0.0 Required SDK核心功能-->
            <!-- 可配置android:process参数将Service放在其他进程中；android:enabled属性不能是false -->
            <!-- 这个是自定义Service，要继承极光JCommonService，可以在更多手机平台上使得推送通道保持的更稳定 -->
            <service android:name=".utils.jiguangPush.JiguangCommonService"
                android:enabled="true"
                android:exported="false"
                android:process=":pushcore">
                <intent-filter>
                    <action android:name="cn.jiguang.user.service.action" />
                </intent-filter>
            </service>

            <!-- 新的 tag/alias 接口结果返回需要开发者配置一个自定义的Receiver -->
            <!-- 该广播需要继承 JPush 提供的 JPushMessageReceiver 类, 并如下新增一个 Intent-Filter -->
            <receiver
                android:name=".utils.jiguangPush.JiguangPushMessageReceiver"
                android:enabled="true"
                android:exported="false" >
                <intent-filter>
                    <action android:name="cn.jpush.android.intent.RECEIVER_MESSAGE" />
                    <category android:name="您应用的包名" />
                </intent-filter>
            </receiver>

            <provider tools:node="remove"
                android:exported="false"
                android:authorities="${applicationId}.jiguang.InitProvider"
                android:name="cn.jpush.android.service.InitProvider"/>

        <!--极光推送 end-->

        <!--优量汇 start-->
            <!--<provider
                android:name="com.qq.e.comm.GDTFileProvider"
                android:authorities="${applicationId}.gdt.fileprovider"
                android:exported="false"
                android:grantUriPermissions="true">
                <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/gdt_file_path"/>
            </provider>

            <uses-library
                android:name="org.apache.http.legacy"
                android:required="false" />
        &lt;!&ndash; 声明SDK所需要的组件 &ndash;&gt;
            <service
                android:name="com.qq.e.comm.DownloadService"
                android:exported="false" />
        &lt;!&ndash; 请开发者注意字母的大小写，ADActivity，而不是AdActivity &ndash;&gt;
            <activity
                android:name="com.qq.e.ads.ADActivity"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
                android:multiprocess="true"/>
            <activity
                android:name="com.qq.e.ads.PortraitADActivity"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
                android:screenOrientation="portrait"
                android:multiprocess="true"/>
            <activity
                android:name="com.qq.e.ads.LandscapeADActivity"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
                android:screenOrientation="sensorLandscape"
                android:multiprocess="true"/>

            <activity
                android:name="com.qq.e.ads.RewardvideoLandscapeADActivity"
                android:screenOrientation="landscape"
                android:configChanges="keyboard|keyboardHidden|orientation|screenSize"
                android:theme="@android:style/Theme.Light.NoTitleBar"
                android:multiprocess="true">
                <meta-data android:name="android.notch_support" android:value="true"/>
            </activity>

            <activity
                android:name="com.qq.e.ads.DialogActivity"
                android:theme="@android:style/Theme.Translucent.NoTitleBar.Fullscreen"
                android:multiprocess="true"/>-->
        <!--优量汇 end-->

        <!--穿山甲 start-->
            <!--<provider
                android:name="com.bytedance.sdk.openadsdk.TTFileProvider"
                android:authorities="${applicationId}.TTFileProvider"
                android:exported="false"
                android:grantUriPermissions="true">
                <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/csj_file_path" />
            </provider>

            <provider
                android:name="com.bytedance.sdk.openadsdk.multipro.TTMultiProvider"
                android:authorities="${applicationId}.TTMultiProvider"
                android:exported="false" />-->
        <!--穿山甲 end-->

        <!--第三方组件选择图片的配置，若不放置到该文件的末尾，可能会报错 start-->
            <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.piccompresstest"
                android:exported="false"
                tools:replace="android:authorities"
                android:grantUriPermissions="true">
                <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/seleter_filepaths"
                    tools:replace="android:resource"/>
            </provider>
        <!--第三方组件选择图片的配置 end-->
        <!-- 二手教材页面 -->
        <activity android:name=".mvp.ui.activity.SecondHandActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>
        
        <!-- 发布旧书页面 -->
        <activity android:name=".mvp.ui.activity.PublishBookActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <!-- 发布闲置页面（二手教材发布） -->
        <activity android:name=".mvp.ui.activity.PublishIdleActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <!-- 助农专区页面 -->
        <activity android:name=".mvp.ui.activity.FarmAssistActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>
        
        <!-- 学习工具页面 -->
        <activity android:name=".mvp.ui.activity.StudyToolActivity"
            android:screenOrientation="portrait"
            android:configChanges="uiMode|orientation|screenSize|smallestScreenSize|screenLayout"/>

        <!-- 简易订单列表页面 -->
        <activity android:name=".mvp.ui.activity.ShopOrderSimpleActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden"/>
    </application>

</manifest>